# Product Requirements Document (PRD)
## Sistema de Envío de Correos con Archivos Adjuntos Reales desde IMAP

## 🎯 OBJETIVO GENERAL
Implementar un servidor MCP (Model Context Protocol) robusto que permita descargar archivos adjuntos reales desde un servidor IMAP y enviarlos a través de Resend API, integrado con Claude Desktop para automatización de envío de facturas y documentos.

## 🏗️ ARQUITECTURA TARGET
```mermaid
graph TD
    A[<PERSON>] --> B[MCP Server Node.js]
    B --> C[Python IMAP Client]
    C --> D[IMAP Server mail.patriciastocker.com:143]
    C --> E[File Processing & Validation]
    E --> F[Base64 Encoding]
    F --> B
    B --> G[Resend API]
    G --> H[Email Delivery]
    
    subgraph "Error Handling"
        I[Connection Retry Logic]
        J[File Validation]
        K[Attachment Size Check]
        L[Content Type Validation]
    end
    
    C --> I
    E --> J
    E --> K
    E --> L
```

## ✅ SUCCESS CRITERIA
### Métricas Técnicas:
- **Response Time**: < 30 segundos para descarga y envío
- **Success Rate**: > 99% para archivos < 10MB
- **File Integrity**: 100% integridad de archivos PDF
- **Error Recovery**: Reintentos automáticos en fallos de conexión

### Métricas Funcionales:
- ✅ Descarga exitosa de "F N 388 REAL TISSUE SPA.pdf" (215.9 KB)
- ✅ Envío <NAME_EMAIL>
- ✅ Archivo adjunto íntegro y funcional
- ✅ Logs detallados para debugging

## 🎯 SCOPE MVP
### INCLUIDO EN MVP:
- ✅ Servidor MCP con herramienta `send_email_with_real_attachment`
- ✅ Conexión IMAP robusta con manejo de errores
- ✅ Descarga de archivos adjuntos específicos por nombre
- ✅ Integración con Resend API
- ✅ Validación de tipos de archivo (PDF, DOC, XLS)
- ✅ Manejo de archivos hasta 40MB (límite Resend)
- ✅ Logging detallado para debugging

### EXCLUIDO DEL MVP:
- ❌ Búsqueda avanzada de correos por criterios complejos
- ❌ Interfaz web de administración
- ❌ Múltiples servidores IMAP
- ❌ Compresión automática de archivos
- ❌ Programación de envíos diferidos

## 🔧 ESPECIFICACIONES TÉCNICAS
### Backend Specifications:
- **Runtime**: Node.js 18+ con CommonJS
- **MCP Framework**: @modelcontextprotocol/sdk ^1.17.1
- **Email Service**: Resend API ^5.0.0
- **Python Integration**: execSync con Python 3.8+
- **IMAP Libraries**: imaplib (built-in), email (built-in)

### IMAP Configuration:
- **Server**: mail.patriciastocker.com
- **Port**: 143 (STARTTLS)
- **Authentication**: <EMAIL> / $Patsto56$
- **Protocol**: IMAP4 with STARTTLS encryption
- **Mailbox**: INBOX (default)

### Resend API Configuration:
- **API Key**: re_KyieBFKx_6swQaoa3VC1FFAgdYVHghfE9
- **Sender**: <EMAIL>
- **Max Attachment Size**: 40MB
- **Supported Types**: PDF, DOC, DOCX, XLS, XLSX, TXT

### Security & Validation:
- **File Type Validation**: MIME type checking
- **Size Limits**: 40MB per attachment (Resend limit)
- **Content Validation**: Base64 encoding verification
- **Error Sanitization**: No sensitive data in error messages

### Performance Targets:
- **IMAP Connection**: < 5 segundos
- **File Download**: < 15 segundos para archivos < 10MB
- **Email Send**: < 10 segundos
- **Total Process**: < 30 segundos end-to-end

## 📊 DIAGRAMAS DE ARQUITECTURA
### Flujo de Datos Principal:
```mermaid
sequenceDiagram
    participant C as Claude Desktop
    participant M as MCP Server
    participant P as Python Script
    participant I as IMAP Server
    participant R as Resend API
    
    C->>M: send_email_with_real_attachment()
    M->>P: execSync Python script
    P->>I: IMAP connect & authenticate
    I-->>P: Connection established
    P->>I: Search emails with attachments
    I-->>P: Email list
    P->>I: Download specific attachment
    I-->>P: File content (binary)
    P->>P: Base64 encode
    P-->>M: JSON response with file data
    M->>R: Send email with attachment
    R-->>M: Email sent confirmation
    M-->>C: Success response
```

### Error Handling Flow:
```mermaid
graph TD
    A[Start Process] --> B{IMAP Connection OK?}
    B -->|No| C[Retry Connection 3x]
    C --> D{Retry Successful?}
    D -->|No| E[Return Connection Error]
    D -->|Yes| F[Search for File]
    B -->|Yes| F
    F --> G{File Found?}
    G -->|No| H[Return File Not Found Error]
    G -->|Yes| I[Download File]
    I --> J{Download Successful?}
    J -->|No| K[Return Download Error]
    J -->|Yes| L[Send Email]
    L --> M{Email Sent?}
    M -->|No| N[Return Send Error]
    M -->|Yes| O[Return Success]
```

## 🔍 CASO DE USO ESPECÍFICO
**Objetivo**: Enviar factura "F N 388 REAL TISSUE SPA.pdf" a <EMAIL>

**Parámetros de entrada**:
```json
{
  "to": "<EMAIL>",
  "subject": "Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA",
  "body": "Estimado cliente,\n\nAdjunto encontrará la factura solicitada.\n\nSaludos cordiales,\nEquipo Patricia Stocker",
  "attachment_filename": "F N 388  REAL TISSUE SPA.pdf"
}
```

**Resultado esperado**:
- ✅ Correo enviado exitosamente
- ✅ Archivo PDF adjunto íntegro (215.9 KB)
- ✅ Confirmación en dashboard de Resend
- ✅ Logs detallados del proceso
