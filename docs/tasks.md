# Task Breakdown Structure
## Sistema de Envío de Correos con Archivos Adjuntos Reales desde IMAP

## 🔴 TAREAS P0 (CRÍTICAS - BLOQUEAN MVP)

### 📋 TAREA P0.1 - Corrección del Bug Crítico en Template String
**🎯 OBJETIVO**: Corregir el error de interpolación de strings en el script Python embebido
**🔗 DEPENDENCIAS**: <PERSON><PERSON><PERSON> (bug fix crítico)
**⏱️ ESTIMACIÓN**: Complejidad: Baja (15 minutos)

#### SUBTAREAS:
##### P0.1.1 - Corregir interpolación de filename en Python script
- 🔍 **Análisis Técnico**: El script Python en `email-attachment-simple.js` línea 199 usa `'${filename}' in part_filename` (string literal) en lugar del valor real del parámetro
- 📊 **Diagrama**: 
```mermaid
graph LR
    A[filename parameter] --> B{Template interpolation}
    B -->|ACTUAL| C["'${filename}' literal string"]
    B -->|EXPECTED| D["actual filename value"]
    C --> E[File never found]
    D --> F[File found successfully]
```
- 🛠️ **Implementación MVP**: Reemplazar `'${filename}'` con interpolación correcta usando f-strings o format()
- 🧪 **Pruebas de Integración**: Ejecutar descarga del archivo "F N 388  REAL TISSUE SPA.pdf" y verificar que se encuentra
- 🔗 **Integraciones**: Script Python embebido en Node.js execSync
- ⚡ **Performance**: Sin impacto en performance, solo corrección de lógica

##### P0.1.2 - Validar manejo de caracteres especiales en nombres de archivo
- 🔍 **Análisis Técnico**: El archivo objetivo tiene espacios dobles "F N 388  REAL TISSUE SPA.pdf"
- 🛠️ **Implementación MVP**: Implementar búsqueda flexible que maneje espacios múltiples y caracteres especiales
- 🧪 **Pruebas de Integración**: Probar con el archivo real que contiene espacios dobles

### 📋 TAREA P0.2 - Mejora del Manejo de Errores y Logging
**🎯 OBJETIVO**: Implementar logging detallado y manejo robusto de errores
**🔗 DEPENDENCIAS**: P0.1 (corrección del bug principal)
**⏱️ ESTIMACIÓN**: Complejidad: Media (30 minutos)

#### SUBTAREAS:
##### P0.2.1 - Implementar logging detallado en el proceso de descarga
- 🔍 **Análisis Técnico**: Actualmente los errores se capturan pero no se logean adecuadamente
- 📊 **Diagrama**:
```mermaid
graph TD
    A[Start Download] --> B[Log: Connecting to IMAP]
    B --> C[Log: Searching for file]
    C --> D[Log: File found/not found]
    D --> E[Log: Download progress]
    E --> F[Log: Conversion to Base64]
    F --> G[Log: Success/Error result]
```
- 🛠️ **Implementación MVP**: Agregar console.error logs en cada etapa del proceso Python
- 🧪 **Pruebas de Integración**: Verificar que los logs aparecen correctamente en la consola del MCP server

##### P0.2.2 - Implementar reintentos automáticos para conexiones IMAP
- 🔍 **Análisis Técnico**: Conexiones IMAP pueden fallar temporalmente
- 🛠️ **Implementación MVP**: Implementar 3 reintentos con backoff exponencial
- 🧪 **Pruebas de Integración**: Simular fallo de conexión y verificar reintentos

### 📋 TAREA P0.3 - Validación y Testing del Flujo Completo
**🎯 OBJETIVO**: Crear pruebas de integración end-to-end
**🔗 DEPENDENCIAS**: P0.1, P0.2 (correcciones principales)
**⏱️ ESTIMACIÓN**: Complejidad: Media (45 minutos)

#### SUBTAREAS:
##### P0.3.1 - Crear script de prueba end-to-end
- 🔍 **Análisis Técnico**: Necesitamos validar el flujo completo desde descarga hasta envío
- 📊 **Diagrama**:
```mermaid
graph LR
    A[Test Script] --> B[Call MCP Tool]
    B --> C[Download File]
    C --> D[Send Email]
    D --> E[Verify in Resend Dashboard]
```
- 🛠️ **Implementación MVP**: Script que llame al MCP server y verifique el resultado
- 🧪 **Pruebas de Integración**: Envío real del archivo "F N 388  REAL TISSUE SPA.pdf" a email de prueba
- 🔗 **Integraciones**: MCP server, IMAP, Resend API
- ⚡ **Performance**: Verificar que el proceso completo toma < 30 segundos

##### P0.3.2 - Validar integridad del archivo adjunto
- 🔍 **Análisis Técnico**: Verificar que el PDF adjunto es idéntico al original
- 🛠️ **Implementación MVP**: Comparar checksums MD5 del archivo original vs descargado
- 🧪 **Pruebas de Integración**: Descargar archivo desde email enviado y comparar con original

## 🟡 TAREAS P1 (ALTAS - IMPORTANTES PARA FUNCIONALIDAD COMPLETA)

### 📋 TAREA P1.1 - Optimización del Servidor MCP
**🎯 OBJETIVO**: Mejorar la arquitectura del servidor MCP para mayor robustez
**🔗 DEPENDENCIAS**: P0.* (MVP funcional)
**⏱️ ESTIMACIÓN**: Complejidad: Media (60 minutos)

#### SUBTAREAS:
##### P1.1.1 - Refactorizar script Python a archivo separado
- 🔍 **Análisis Técnico**: El script Python embebido es difícil de mantener y debuggear
- 🛠️ **Implementación**: Crear `src/python/imap_downloader.py` como módulo independiente
- 🧪 **Pruebas de Integración**: Verificar que el módulo separado funciona igual que el embebido

##### P1.1.2 - Implementar validación de parámetros de entrada
- 🔍 **Análisis Técnico**: Validar email, subject, filename antes de procesar
- 🛠️ **Implementación**: Usar esquemas de validación con zod o similar
- 🧪 **Pruebas de Integración**: Probar con parámetros inválidos y verificar errores apropiados

### 📋 TAREA P1.2 - Mejoras de Seguridad y Configuración
**🎯 OBJETIVO**: Implementar mejores prácticas de seguridad
**🔗 DEPENDENCIAS**: P1.1 (arquitectura mejorada)
**⏱️ ESTIMACIÓN**: Complejidad: Media (45 minutos)

#### SUBTAREAS:
##### P1.2.1 - Externalizar credenciales a archivo de configuración
- 🔍 **Análisis Técnico**: Las credenciales IMAP están hardcodeadas en el código
- 🛠️ **Implementación**: Crear `config/email.json` con credenciales encriptadas
- 🧪 **Pruebas de Integración**: Verificar que la configuración externa funciona

##### P1.2.2 - Implementar sanitización de nombres de archivo
- 🔍 **Análisis Técnico**: Prevenir inyección de comandos a través de nombres de archivo
- 🛠️ **Implementación**: Whitelist de caracteres permitidos en nombres de archivo
- 🧪 **Pruebas de Integración**: Probar con nombres de archivo maliciosos

## 🟢 TAREAS P2 (MEDIAS - MEJORAS DE EXPERIENCIA)

### 📋 TAREA P2.1 - Optimizaciones de Performance
**🎯 OBJETIVO**: Mejorar velocidad y eficiencia del sistema
**🔗 DEPENDENCIAS**: P1.* (funcionalidad completa)
**⏱️ ESTIMACIÓN**: Complejidad: Alta (90 minutos)

#### SUBTAREAS:
##### P2.1.1 - Implementar caché de conexiones IMAP
- 🔍 **Análisis Técnico**: Reutilizar conexiones IMAP para múltiples operaciones
- 🛠️ **Implementación**: Pool de conexiones con timeout automático
- 🧪 **Pruebas de Integración**: Verificar mejora en tiempo de respuesta

##### P2.1.2 - Optimizar búsqueda de archivos adjuntos
- 🔍 **Análisis Técnico**: Búsqueda actual revisa todos los correos secuencialmente
- 🛠️ **Implementación**: Índice de archivos adjuntos o búsqueda por criterios IMAP
- 🧪 **Pruebas de Integración**: Medir tiempo de búsqueda antes y después

### 📋 TAREA P2.2 - Funcionalidades Adicionales
**🎯 OBJETIVO**: Agregar características útiles para el usuario
**🔗 DEPENDENCIAS**: P2.1 (optimizaciones base)
**⏱️ ESTIMACIÓN**: Complejidad: Media (60 minutos)

#### SUBTAREAS:
##### P2.2.1 - Soporte para múltiples archivos adjuntos
- 🔍 **Análisis Técnico**: Permitir envío de múltiples archivos en un solo correo
- 🛠️ **Implementación**: Modificar parámetros para aceptar array de filenames
- 🧪 **Pruebas de Integración**: Enviar correo con 2-3 archivos adjuntos

##### P2.2.2 - Implementar preview de archivos antes del envío
- 🔍 **Análisis Técnico**: Mostrar información del archivo (tamaño, tipo) antes de enviar
- 🛠️ **Implementación**: Herramienta MCP adicional para preview
- 🧪 **Pruebas de Integración**: Verificar que el preview muestra información correcta

## 🔵 TAREAS P3 (BAJAS - OPTIMIZACIONES)

### 📋 TAREA P3.1 - Monitoreo y Métricas
**🎯 OBJETIVO**: Implementar monitoreo del sistema
**🔗 DEPENDENCIAS**: P2.* (sistema optimizado)
**⏱️ ESTIMACIÓN**: Complejidad: Alta (120 minutos)

#### SUBTAREAS:
##### P3.1.1 - Implementar métricas de performance
- 🔍 **Análisis Técnico**: Recopilar métricas de tiempo de respuesta, tasa de éxito, etc.
- 🛠️ **Implementación**: Sistema de métricas con exportación a JSON
- 🧪 **Pruebas de Integración**: Verificar que las métricas se recopilan correctamente

##### P3.1.2 - Dashboard de monitoreo básico
- 🔍 **Análisis Técnico**: Interfaz web simple para ver estado del sistema
- 🛠️ **Implementación**: Página HTML estática con métricas en tiempo real
- 🧪 **Pruebas de Integración**: Verificar que el dashboard muestra datos actualizados
