# 🔧 CONFIGURAC<PERSON><PERSON><PERSON> DE CLAUDE DESKTOP PARA MCP

## 📍 **UBICACIÓN DEL ARCHIVO DE CONFIGURACIÓN**

El archivo de configuración de Claude Desktop debe estar en:
```
/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json
```

## 📝 **CONFIGURACIÓN CORRECTA**

Copia y pega exactamente este contenido en el archivo `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "email-with-real-attachments": {
      "command": "node",
      "args": ["/Users/<USER>/Desktop/PS/resend-mcp/email-attachment-simple.js"],
      "env": {
        "RESEND_API_KEY": "re_KyieBFKx_6swQaoa3VC1FFAgdYVHghfE9",
        "SENDER_EMAIL_ADDRESS": "<EMAIL>"
      }
    }
  }
}
```

## 🚨 **PASOS IMPORTANTES**

### 1. **ELIMINAR CONFIGURACIONES CONFLICTIVAS**
Si tienes otros servidores MCP configurados que puedan causar conflictos, elimínalos temporalmente:
- `smart-email`
- `email-reader` 
- `email-attachment-mcp`
- Cualquier otro servidor de email

### 2. **REINICIAR CLAUDE DESKTOP**
Después de cambiar la configuración:
1. Cierra completamente Claude Desktop
2. Espera 5 segundos
3. Abre Claude Desktop nuevamente

### 3. **VERIFICAR LA HERRAMIENTA**
La herramienta disponible debe ser:
- **Nombre**: `send_email_with_real_attachment`
- **Descripción**: "Envía un correo con archivo adjunto real descargado del servidor IMAP"

## 📧 **FORMATO CORRECTO DEL PROMPT**

Usa exactamente este formato:

```
Necesito enviar un correo con un archivo adjunto real. Por favor usa la herramienta "send_email_with_real_attachment" para:

- Destinatario: <EMAIL>
- Asunto: "Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA"  
- Mensaje: "Estimado/a, adjunto la factura N° 388 de Real Tissue SPA por $686.470 correspondiente a tasas finales NUVOLA. Saludos cordiales, Marcas Patricia Stocker"
- Archivo adjunto: "F N 388  REAL TISSUE SPA.pdf"

El sistema debe descargar el archivo PDF real del servidor IMAP y enviarlo adjunto.
```

## 🔍 **DIAGNÓSTICO DE PROBLEMAS**

### Si Claude Desktop no encuentra la herramienta:
1. Verifica que el archivo `email-attachment-simple.js` existe en la ruta especificada
2. Verifica que Node.js está instalado y accesible
3. Reinicia Claude Desktop completamente

### Si aparece error de permisos:
1. Verifica que el archivo tiene permisos de ejecución:
   ```bash
   chmod +x /Users/<USER>/Desktop/PS/resend-mcp/email-attachment-simple.js
   ```

### Si aparece error de variables de entorno:
1. Verifica que las variables están correctamente configuradas en el JSON
2. No uses comillas adicionales en los valores

## ✅ **VERIFICACIÓN DE FUNCIONAMIENTO**

Cuando funcione correctamente, deberías ver:
1. Claude Desktop reconoce la herramienta `send_email_with_real_attachment`
2. El proceso descarga el archivo desde IMAP (logs visibles)
3. El correo se envía exitosamente con ID de Resend
4. El archivo adjunto llega íntegro al destinatario

## 🎯 **RESULTADO ESPERADO**

```
✅ Correo enviado exitosamente con archivo adjunto REAL

ID de envío: [ID-de-Resend]
Destinatarios: <EMAIL>
Asunto: Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA
Archivo adjunto: F N 388  REAL TISSUE SPA.pdf (215.9 KB)

📧 El archivo PDF real ha sido descargado del servidor IMAP y enviado correctamente.
```
