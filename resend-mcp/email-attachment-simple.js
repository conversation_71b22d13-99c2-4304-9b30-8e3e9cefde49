#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { Resend } = require('resend');
const { execSync } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class SimpleEmailAttachmentMCP {
  constructor() {
    this.server = new Server(
      {
        name: 'simple-email-attachment-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.resendApiKey = process.env.RESEND_API_KEY;
    this.senderEmail = process.env.SENDER_EMAIL_ADDRESS;
    this.tempDir = '/tmp/mcp-email-simple';
    
    if (!this.resendApiKey) {
      console.error('Error: RESEND_API_KEY environment variable is required');
      process.exit(1);
    }

    this.resend = new Resend(this.resendApiKey);
    this.setupToolHandlers();
    this.ensureTempDir();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Error creating temp directory:', error);
    }
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'send_email_with_real_attachment',
            description: 'Envía un correo con archivo adjunto real descargado del servidor IMAP',
            inputSchema: {
              type: 'object',
              properties: {
                to: {
                  oneOf: [
                    { type: 'string', description: 'Destinatario único del correo' },
                    { type: 'array', items: { type: 'string' }, description: 'Lista de destinatarios del correo' }
                  ],
                  description: 'Destinatario(s) del correo'
                },
                subject: {
                  type: 'string',
                  description: 'Asunto del correo'
                },
                body: {
                  type: 'string',
                  description: 'Cuerpo del correo'
                },
                attachment_filename: {
                  type: 'string',
                  description: 'Nombre del archivo a buscar y adjuntar',
                  default: 'F N 388  REAL TISSUE SPA.pdf'
                }
              },
              required: ['to', 'subject', 'body']
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case 'send_email_with_real_attachment':
          return await this.handleSendEmailWithRealAttachment(request.params.arguments);
        default:
          throw new Error(`Unknown tool: ${request.params.name}`);
      }
    });
  }

  async handleSendEmailWithRealAttachment(args) {
    try {
      console.error(`[Simple Email MCP] Enviando correo con archivo real: ${args.attachment_filename || 'F N 388  REAL TISSUE SPA.pdf'}`);
      
      // 1. Descargar el archivo real usando Python directo
      const attachment = await this.downloadRealAttachment(args.attachment_filename || 'F N 388  REAL TISSUE SPA.pdf');
      
      if (!attachment) {
        throw new Error('No se pudo descargar el archivo adjunto del servidor IMAP');
      }

      // 2. Preparar el correo con Resend
      const emailData = {
        from: this.senderEmail || '<EMAIL>',
        to: Array.isArray(args.to) ? args.to : [args.to],
        subject: args.subject,
        text: args.body,
        attachments: [
          {
            filename: attachment.filename,
            content: attachment.content, // Buffer
            type: attachment.content_type
          }
        ]
      };

      // 3. Enviar con Resend
      console.error(`[Simple Email MCP] Enviando correo a Resend API...`);
      console.error(`[Simple Email MCP] Datos del correo: ${JSON.stringify({
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        attachments: emailData.attachments.map(att => ({
          filename: att.filename,
          type: att.type,
          size: att.content.length
        }))
      }, null, 2)}`);

      const result = await this.resend.emails.send(emailData);

      console.error(`[Simple Email MCP] Respuesta de Resend: ${JSON.stringify(result, null, 2)}`);

      // 4. Limpiar archivo temporal
      if (attachment.tempPath) {
        try {
          await fs.unlink(attachment.tempPath);
        } catch (e) {
          // Ignorar errores de limpieza
        }
      }

      return {
        content: [
          {
            type: 'text',
            text: `✅ **Correo enviado exitosamente con archivo adjunto REAL**

**ID de envío:** ${result.data?.id || 'unknown'}
**Destinatarios:** ${Array.isArray(args.to) ? args.to.join(', ') : args.to}
**Asunto:** ${args.subject}
**Archivo adjunto:** ${attachment.filename} (${attachment.size})

📧 El archivo PDF real ha sido descargado del servidor IMAP y enviado correctamente.`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error enviando correo con archivo adjunto:** ${error.message}

**Detalles del error:**
- Destinatarios: ${Array.isArray(args.to) ? args.to.join(', ') : args.to}
- Asunto: ${args.subject}
- Archivo buscado: ${args.attachment_filename || 'F N 388  REAL TISSUE SPA.pdf'}

**Posibles causas:**
1. Error de conexión al servidor IMAP
2. Archivo no encontrado en los correos
3. Error en la API de Resend`
          }
        ],
        isError: true
      };
    }
  }

  async downloadRealAttachment(filename) {
    try {
      console.error(`[Simple Email MCP] Descargando archivo real: ${filename}`);
      
      // Script Python simplificado que funciona
      const pythonScript = `
import imaplib
import email
import base64
import json
import sys
import re

try:
    # Conectar al servidor IMAP
    print("🔗 Conectando a servidor IMAP...", file=sys.stderr)
    mail = imaplib.IMAP4('mail.patriciastocker.com', 143)
    mail.starttls()
    mail.login('<EMAIL>', '$Patsto56$')
    mail.select('inbox')
    print("✅ Conexión IMAP exitosa", file=sys.stderr)

    # Buscar correos recientes
    print("🔍 Buscando correos con archivos adjuntos...", file=sys.stderr)
    status, messages = mail.search(None, 'ALL')
    mail_ids = messages[0].split()[-10:]
    print(f"📬 Revisando {len(mail_ids)} correos recientes", file=sys.stderr)

    target_filename = "${filename.replace(/"/g, '\\"')}"
    print(f"🎯 Buscando archivo: '{target_filename}'", file=sys.stderr)

    for mail_id in reversed(mail_ids):
        status, msg_data = mail.fetch(mail_id, '(RFC822)')
        msg = email.message_from_bytes(msg_data[0][1])

        for part in msg.walk():
            if part.get_content_disposition() == 'attachment':
                part_filename = part.get_filename()
                if part_filename:
                    print(f"📎 Archivo encontrado: '{part_filename}'", file=sys.stderr)

                    # Normalizar espacios múltiples y comparar
                    normalized_part = re.sub(r'\\s+', ' ', part_filename.strip())
                    normalized_target = re.sub(r'\\s+', ' ', target_filename.strip())

                    if normalized_part == normalized_target or target_filename in part_filename:
                        print(f"🎉 ¡Archivo coincidente encontrado: '{part_filename}'!", file=sys.stderr)
                        content = part.get_payload(decode=True)
                        if content:
                            content_base64 = base64.b64encode(content).decode('utf-8')
                            print(f"✅ Archivo descargado exitosamente: {len(content)} bytes", file=sys.stderr)

                            result = {
                                'filename': part_filename,
                                'content_type': part.get_content_type(),
                                'size': len(content),
                                'content': content_base64
                            }

                            print(json.dumps(result))
                            mail.close()
                            mail.logout()
                            sys.exit(0)
                        else:
                            print(f"❌ Error: contenido vacío para '{part_filename}'", file=sys.stderr)

    print(f"❌ Archivo '{target_filename}' no encontrado en los correos revisados", file=sys.stderr)
    print('{"error": "File not found"}')
    mail.close()
    mail.logout()

except Exception as e:
    print(f"💥 Error en script Python: {str(e)}", file=sys.stderr)
    print(f'{{"error": "{str(e)}"}}')
`;

      // Ejecutar Python directamente con interpolación correcta
      const escapedScript = pythonScript.replace(/"/g, '\\"').replace(/\$/g, '\\$');
      const result = execSync(`python3 -c "${escapedScript}"`, {
        encoding: 'utf8',
        timeout: 30000
      });

      const data = JSON.parse(result.trim());
      
      if (data.error) {
        throw new Error(data.error);
      }

      // Convertir Base64 a Buffer
      const content = Buffer.from(data.content, 'base64');
      
      return {
        filename: data.filename,
        content_type: data.content_type,
        size: `${(data.size / 1024).toFixed(1)} KB`,
        content: content,
        tempPath: null
      };
      
    } catch (error) {
      console.error(`[Simple Email MCP] Error descargando archivo: ${error.message}`);
      return null;
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Simple Email Attachment MCP server running on stdio');
  }
}

if (require.main === module) {
  const server = new SimpleEmailAttachmentMCP();
  server.run().catch(console.error);
}

module.exports = SimpleEmailAttachmentMCP;
