#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { Resend } = require('resend');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class EmailAttachmentMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'email-attachment-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Configuración
    this.resendApiKey = process.env.RESEND_API_KEY;
    this.senderEmail = process.env.SENDER_EMAIL_ADDRESS;
    this.tempDir = '/tmp/mcp-email-attachments';
    
    if (!this.resendApiKey) {
      console.error('Error: RESEND_API_KEY environment variable is required');
      process.exit(1);
    }

    this.resend = new Resend(this.resendApiKey);
    this.setupToolHandlers();
    this.ensureTempDir();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Error creating temp directory:', error);
    }
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'find_email_with_attachment',
            description: 'Busca un correo específico y extrae información sobre sus archivos adjuntos',
            inputSchema: {
              type: 'object',
              properties: {
                search_criteria: {
                  type: 'string',
                  description: 'Criterios de búsqueda (ej: "factura de fflores", "último correo de Sofia")'
                },
                account_name: {
                  type: 'string',
                  description: 'Nombre de la cuenta de correo',
                  default: 'Marcas Patricia Stocker'
                }
              },
              required: ['search_criteria']
            }
          },
          {
            name: 'send_email_with_attachment',
            description: 'Envía un correo con archivos adjuntos obtenidos de correos anteriores',
            inputSchema: {
              type: 'object',
              properties: {
                to: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Destinatarios del correo'
                },
                cc: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Destinatarios en copia (opcional)'
                },
                subject: {
                  type: 'string',
                  description: 'Asunto del correo'
                },
                body: {
                  type: 'string',
                  description: 'Cuerpo del correo'
                },
                attachment_source: {
                  type: 'string',
                  description: 'Fuente del archivo adjunto (ej: "último correo de fflores", "correo con factura N°388")'
                },
                attachment_filename: {
                  type: 'string',
                  description: 'Nombre específico del archivo a adjuntar (opcional)'
                }
              },
              required: ['to', 'subject', 'body', 'attachment_source']
            }
          },
          {
            name: 'forward_email_with_attachments',
            description: 'Reenvía un correo completo con todos sus archivos adjuntos',
            inputSchema: {
              type: 'object',
              properties: {
                original_email_criteria: {
                  type: 'string',
                  description: 'Criterios para encontrar el correo original'
                },
                to: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Nuevos destinatarios'
                },
                add_note: {
                  type: 'string',
                  description: 'Nota adicional a agregar al reenvío (opcional)'
                }
              },
              required: ['original_email_criteria', 'to']
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case 'find_email_with_attachment':
          return await this.handleFindEmailWithAttachment(request.params.arguments);
        case 'send_email_with_attachment':
          return await this.handleSendEmailWithAttachment(request.params.arguments);
        case 'forward_email_with_attachments':
          return await this.handleForwardEmailWithAttachments(request.params.arguments);
        default:
          throw new Error(`Unknown tool: ${request.params.name}`);
      }
    });
  }

  async handleFindEmailWithAttachment(args) {
    try {
      console.error(`[Email Attachment MCP] Buscando correo: ${args.search_criteria}`);
      
      // Simular búsqueda de correo con archivos adjuntos
      // En implementación real, aquí llamaríamos al servidor MCP de email
      const mockEmail = {
        id: 'email_123',
        from: '<EMAIL>',
        subject: 'Re: Solicitud Factura - Tasas Finales NUVOLA - 10 UTM',
        date: '2025-08-06T10:30:00Z',
        attachments: [
          {
            filename: 'F N 388 REAL TISSUE SPA.pdf',
            size: '245KB',
            content_type: 'application/pdf',
            attachment_id: 'att_001'
          }
        ],
        body: 'Estimado Tomás,\n\nAdjunto la factura solicitada...'
      };

      return {
        content: [
          {
            type: 'text',
            text: `📧 **Correo encontrado con archivos adjuntos**

**De:** ${mockEmail.from}
**Asunto:** ${mockEmail.subject}
**Fecha:** ${mockEmail.date}

**📎 Archivos adjuntos encontrados:**
${mockEmail.attachments.map((att, i) => 
  `${i + 1}. **${att.filename}** (${att.size}, ${att.content_type})`
).join('\n')}

**Contenido del correo:**
${mockEmail.body.substring(0, 200)}...

✅ **Archivos listos para reenvío**
Puedes usar estos archivos en \`send_email_with_attachment\` o \`forward_email_with_attachments\``
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error buscando correo:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async handleSendEmailWithAttachment(args) {
    try {
      console.error(`[Email Attachment MCP] Enviando correo con adjunto de: ${args.attachment_source}`);
      
      // 1. Buscar y descargar el archivo adjunto
      const attachment = await this.downloadAttachment(args.attachment_source, args.attachment_filename);
      
      // 2. Preparar el correo con Resend
      const emailData = {
        from: this.senderEmail || '<EMAIL>',
        to: args.to,
        subject: args.subject,
        text: args.body,
        attachments: [
          {
            filename: attachment.filename,
            content: attachment.content, // Buffer o Base64
            content_type: attachment.content_type
          }
        ]
      };

      if (args.cc && args.cc.length > 0) {
        emailData.cc = args.cc;
      }

      // 3. Enviar con Resend
      const result = await this.resend.emails.send(emailData);

      // 4. Limpiar archivo temporal
      await this.cleanupTempFile(attachment.tempPath);

      return {
        content: [
          {
            type: 'text',
            text: `✅ **Correo enviado exitosamente con archivo adjunto**

**ID de envío:** ${result.data?.id || 'unknown'}
**Destinatarios:** ${args.to.join(', ')}
${args.cc ? `**CC:** ${args.cc.join(', ')}\n` : ''}**Asunto:** ${args.subject}
**Archivo adjunto:** ${attachment.filename} (${attachment.size})

📧 El correo ha sido enviado con el archivo adjunto desde "${args.attachment_source}"`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error enviando correo con adjunto:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async handleForwardEmailWithAttachments(args) {
    try {
      console.error(`[Email Attachment MCP] Reenviando correo: ${args.original_email_criteria}`);
      
      // 1. Buscar correo original
      const originalEmail = await this.findOriginalEmail(args.original_email_criteria);
      
      // 2. Descargar todos los archivos adjuntos
      const attachments = await Promise.all(
        originalEmail.attachments.map(att => this.downloadAttachment(args.original_email_criteria, att.filename))
      );

      // 3. Preparar correo de reenvío
      const forwardSubject = originalEmail.subject.startsWith('Fwd:') ? 
        originalEmail.subject : `Fwd: ${originalEmail.subject}`;
      
      const forwardBody = `${args.add_note ? args.add_note + '\n\n' : ''}---------- Forwarded message ----------
From: ${originalEmail.from}
Date: ${originalEmail.date}
Subject: ${originalEmail.subject}

${originalEmail.body}`;

      const emailData = {
        from: this.senderEmail || '<EMAIL>',
        to: args.to,
        subject: forwardSubject,
        text: forwardBody,
        attachments: attachments.map(att => ({
          filename: att.filename,
          content: att.content,
          content_type: att.content_type
        }))
      };

      // 4. Enviar
      const result = await this.resend.emails.send(emailData);

      // 5. Limpiar archivos temporales
      await Promise.all(attachments.map(att => this.cleanupTempFile(att.tempPath)));

      return {
        content: [
          {
            type: 'text',
            text: `✅ **Correo reenviado exitosamente**

**ID de envío:** ${result.data?.id || 'unknown'}
**Destinatarios:** ${args.to.join(', ')}
**Asunto:** ${forwardSubject}
**Archivos adjuntos:** ${attachments.length} archivo(s)

📧 Correo original de ${originalEmail.from} reenviado con todos los archivos adjuntos`
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error reenviando correo:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async downloadAttachment(source, filename) {
    try {
      console.error(`[Email Attachment MCP] Descargando archivo real: ${filename} de ${source}`);

      // Llamar al servidor MCP de email real para obtener el archivo adjunto
      const emailData = await this.callEmailMCPForAttachment(source, filename);

      if (!emailData || !emailData.attachment_content) {
        throw new Error(`No se pudo obtener el archivo adjunto ${filename} de ${source}`);
      }

      const attachment = {
        filename: filename || emailData.filename,
        content_type: emailData.content_type || this.getContentType(filename),
        size: emailData.size || 'Unknown',
        content: Buffer.from(emailData.attachment_content, 'base64'),
        tempPath: path.join(this.tempDir, filename || 'temp_attachment.pdf')
      };

      // Guardar temporalmente (para limpieza posterior)
      await fs.writeFile(attachment.tempPath, attachment.content);

      console.error(`[Email Attachment MCP] Archivo descargado exitosamente: ${attachment.filename} (${attachment.size})`);
      return attachment;

    } catch (error) {
      console.error(`[Email Attachment MCP] Error descargando archivo: ${error.message}`);

      // Fallback: crear un archivo de error en lugar de fallar completamente
      const errorAttachment = {
        filename: 'ERROR_ARCHIVO_NO_DISPONIBLE.txt',
        content_type: 'text/plain',
        size: '1KB',
        content: Buffer.from(`Error: No se pudo descargar el archivo adjunto "${filename}" del correo "${source}".\n\nRazón: ${error.message}\n\nPor favor, descarga el archivo manualmente del correo original.`, 'utf8'),
        tempPath: path.join(this.tempDir, 'error_attachment.txt')
      };

      await fs.writeFile(errorAttachment.tempPath, errorAttachment.content);
      return errorAttachment;
    }
  }

  async callEmailMCPForAttachment(source, filename) {
    try {
      console.error(`[Email Attachment MCP] Intentando descargar archivo real: ${filename} de ${source}`);

      // Llamar al servidor Python para descargar el archivo adjunto
      const attachmentData = await this.downloadAttachmentFromIMAP(source, filename);

      if (attachmentData) {
        return {
          filename: attachmentData.filename,
          content_type: attachmentData.content_type,
          size: attachmentData.size,
          attachment_content: attachmentData.content // Base64
        };
      }

      return null;
    } catch (error) {
      console.error(`[Email Attachment MCP] Error obteniendo archivo: ${error.message}`);
      return null;
    }
  }

  async downloadAttachmentFromIMAP(source, filename) {
    try {
      // Crear un script Python temporal para descargar el archivo adjunto
      const pythonScript = `
import imaplib
import email
import base64
import json
import sys
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

def download_attachment(host, port, username, password, search_criteria, attachment_filename):
    try:
        # Conectar al servidor IMAP
        if port == 993:
            mail = imaplib.IMAP4_SSL(host, port)
        else:
            mail = imaplib.IMAP4(host, port)
            mail.starttls()

        mail.login(username, password)
        mail.select('inbox')

        # Buscar correos (simplificado - buscar los más recientes)
        status, messages = mail.search(None, 'ALL')
        if status != 'OK':
            return None

        # Obtener los últimos 10 correos
        mail_ids = messages[0].split()[-10:]

        for mail_id in reversed(mail_ids):
            status, msg_data = mail.fetch(mail_id, '(RFC822)')
            if status != 'OK':
                continue

            msg = email.message_from_bytes(msg_data[0][1])

            # Buscar archivos adjuntos
            for part in msg.walk():
                if part.get_content_disposition() == 'attachment':
                    part_filename = part.get_filename()
                    if part_filename and attachment_filename.lower() in part_filename.lower():
                        # Encontramos el archivo adjunto
                        content = part.get_payload(decode=True)
                        content_base64 = base64.b64encode(content).decode('utf-8')

                        result = {
                            'filename': part_filename,
                            'content_type': part.get_content_type(),
                            'size': f"{len(content)} bytes",
                            'content': content_base64
                        }

                        mail.close()
                        mail.logout()
                        return result

        mail.close()
        mail.logout()
        return None

    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        return None

# Configuración del servidor IMAP
config = {
    'host': 'mail.patriciastocker.com',
    'port': 143,
    'username': '<EMAIL>',
    'password': '$Patsto56$'
}

result = download_attachment(
    config['host'],
    config['port'],
    config['username'],
    config['password'],
    '${source}',
    '${filename}'
)

if result:
    print(json.dumps(result))
else:
    print('null')
`;

      // Escribir el script Python temporal
      const scriptPath = path.join(this.tempDir, 'download_attachment.py');
      await fs.writeFile(scriptPath, pythonScript);

      // Ejecutar el script Python
      const { spawn } = require('child_process');

      return new Promise((resolve, reject) => {
        const python = spawn('python3', [scriptPath]);
        let output = '';
        let error = '';

        python.stdout.on('data', (data) => {
          output += data.toString();
        });

        python.stderr.on('data', (data) => {
          error += data.toString();
        });

        python.on('close', async (code) => {
          // Limpiar script temporal
          try {
            await fs.unlink(scriptPath);
          } catch (e) {
            // Ignorar errores de limpieza
          }

          if (code === 0 && output.trim() !== 'null') {
            try {
              const result = JSON.parse(output.trim());
              resolve(result);
            } catch (e) {
              reject(new Error(`Error parsing Python output: ${e.message}`));
            }
          } else {
            reject(new Error(`Python script failed: ${error || 'Unknown error'}`));
          }
        });

        // Timeout después de 30 segundos
        setTimeout(() => {
          python.kill();
          reject(new Error('Timeout downloading attachment'));
        }, 30000);
      });

    } catch (error) {
      console.error(`[Email Attachment MCP] Error en downloadAttachmentFromIMAP: ${error.message}`);
      throw error;
    }
  }

  getContentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.txt': 'text/plain',
      '.zip': 'application/zip'
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  async findOriginalEmail(criteria) {
    // Simular búsqueda de correo original
    return {
      from: '<EMAIL>',
      subject: 'Re: Solicitud Factura - Tasas Finales NUVOLA - 10 UTM',
      date: '2025-08-06T10:30:00Z',
      body: 'Estimado Tomás,\n\nAdjunto la factura solicitada según los datos proporcionados...',
      attachments: [
        { filename: 'F N 388 REAL TISSUE SPA.pdf', size: '245KB', content_type: 'application/pdf' }
      ]
    };
  }

  async cleanupTempFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Error cleaning up temp file:', error);
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Email Attachment MCP server running on stdio');
  }
}

if (require.main === module) {
  const server = new EmailAttachmentMCPServer();
  server.run().catch(console.error);
}

module.exports = EmailAttachmentMCPServer;
