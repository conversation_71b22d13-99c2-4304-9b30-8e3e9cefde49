#!/usr/bin/env node

const { execSync } = require('child_process');

// Test the fixed Python script with the actual filename
const filename = "F N 388  REAL TISSUE SPA.pdf";

console.log(`🧪 Testing download of: "${filename}"`);

const pythonScript = `
import imaplib
import email
import base64
import json
import sys
import re

try:
    # Conectar al servidor IMAP
    print("🔗 Conectando a servidor IMAP...", file=sys.stderr)
    mail = imaplib.IMAP4('mail.patriciastocker.com', 143)
    mail.starttls()
    mail.login('<EMAIL>', '$Patsto56$')
    mail.select('inbox')
    print("✅ Conexión IMAP exitosa", file=sys.stderr)
    
    # Buscar correos recientes
    print("🔍 Buscando correos con archivos adjuntos...", file=sys.stderr)
    status, messages = mail.search(None, 'ALL')
    mail_ids = messages[0].split()[-10:]
    print(f"📬 Revisando {len(mail_ids)} correos recientes", file=sys.stderr)
    
    target_filename = "${filename.replace(/"/g, '\\"')}"
    print(f"🎯 Buscando archivo: '{target_filename}'", file=sys.stderr)
    
    for mail_id in reversed(mail_ids):
        status, msg_data = mail.fetch(mail_id, '(RFC822)')
        msg = email.message_from_bytes(msg_data[0][1])
        
        for part in msg.walk():
            if part.get_content_disposition() == 'attachment':
                part_filename = part.get_filename()
                if part_filename:
                    print(f"📎 Archivo encontrado: '{part_filename}'", file=sys.stderr)
                    
                    # Normalizar espacios múltiples y comparar
                    normalized_part = re.sub(r'\\s+', ' ', part_filename.strip())
                    normalized_target = re.sub(r'\\s+', ' ', target_filename.strip())
                    
                    if normalized_part == normalized_target or target_filename in part_filename:
                        print(f"🎉 ¡Archivo coincidente encontrado: '{part_filename}'!", file=sys.stderr)
                        content = part.get_payload(decode=True)
                        if content:
                            content_base64 = base64.b64encode(content).decode('utf-8')
                            print(f"✅ Archivo descargado exitosamente: {len(content)} bytes", file=sys.stderr)
                            
                            result = {
                                'filename': part_filename,
                                'content_type': part.get_content_type(),
                                'size': len(content),
                                'content': content_base64
                            }
                            
                            print(json.dumps(result))
                            mail.close()
                            mail.logout()
                            sys.exit(0)
                        else:
                            print(f"❌ Error: contenido vacío para '{part_filename}'", file=sys.stderr)
    
    print(f"❌ Archivo '{target_filename}' no encontrado en los correos revisados", file=sys.stderr)
    print('{"error": "File not found"}')
    mail.close()
    mail.logout()
    
except Exception as e:
    print(f"💥 Error en script Python: {str(e)}", file=sys.stderr)
    print(f'{{"error": "{str(e)}"}}')
`;

try {
    console.log("🚀 Ejecutando script Python...");
    
    // Ejecutar Python directamente con interpolación correcta
    const escapedScript = pythonScript.replace(/"/g, '\\"').replace(/\$/g, '\\$');
    const result = execSync(`python3 -c "${escapedScript}"`, {
        encoding: 'utf8',
        timeout: 30000
    });

    console.log("📊 Resultado:");
    const data = JSON.parse(result.trim());
    
    if (data.error) {
        console.error(`❌ Error: ${data.error}`);
    } else {
        console.log(`✅ Archivo descargado exitosamente:`);
        console.log(`   📄 Nombre: ${data.filename}`);
        console.log(`   📏 Tamaño: ${(data.size / 1024).toFixed(1)} KB`);
        console.log(`   🏷️  Tipo: ${data.content_type}`);
        console.log(`   📦 Contenido Base64: ${data.content.substring(0, 50)}...`);
    }
    
} catch (error) {
    console.error(`💥 Error ejecutando script: ${error.message}`);
}
