#!/usr/bin/env node

const { Resend } = require('resend');
const { execSync } = require('child_process');

console.log("🔍 DIAGNÓSTICO COMPLETO DE RESEND API");
console.log("====================================");

// Configuración
const RESEND_API_KEY = 're_KyieBFKx_6swQaoa3VC1FFAgdYVHghfE9';
const SENDER_EMAIL = '<EMAIL>';
const RECIPIENT = '<EMAIL>';

console.log(`🔑 API Key: ${RESEND_API_KEY.substring(0, 10)}...`);
console.log(`📧 Sender: ${SENDER_EMAIL}`);
console.log(`📨 Recipient: ${RECIPIENT}`);

const resend = new Resend(RESEND_API_KEY);

async function testResendAPI() {
    try {
        console.log("\n🧪 PASO 1: Verificando API Key y configuración");
        
        // Test básico sin archivo adjunto
        console.log("📤 Enviando correo de prueba SIN archivo adjunto...");
        
        const basicEmailData = {
            from: SENDER_EMAIL,
            to: RECIPIENT,
            subject: "Test - Verificación de Resend API",
            text: "Este es un correo de prueba para verificar que Resend API funciona correctamente.\n\nSi recibes este correo, la configuración básica está funcionando."
        };

        const basicResult = await resend.emails.send(basicEmailData);
        console.log("✅ Correo básico enviado:");
        console.log(`   📧 ID: ${basicResult.data?.id || 'unknown'}`);
        console.log(`   📊 Status: ${basicResult.error ? 'ERROR' : 'SUCCESS'}`);
        
        if (basicResult.error) {
            console.error(`   ❌ Error: ${JSON.stringify(basicResult.error, null, 2)}`);
            return false;
        }

        console.log("\n🧪 PASO 2: Descargando archivo real del IMAP");
        
        // Descargar archivo real
        const filename = "F N 388  REAL TISSUE SPA.pdf";
        console.log(`🎯 Descargando: ${filename}`);
        
        const pythonScript = `
import imaplib
import email
import base64
import json
import sys
import re

try:
    print("🔗 Conectando a IMAP...", file=sys.stderr)
    mail = imaplib.IMAP4('mail.patriciastocker.com', 143)
    mail.starttls()
    mail.login('<EMAIL>', '$Patsto56$')
    mail.select('inbox')
    
    status, messages = mail.search(None, 'ALL')
    mail_ids = messages[0].split()[-10:]
    
    target_filename = "${filename.replace(/"/g, '\\"')}"
    
    for mail_id in reversed(mail_ids):
        status, msg_data = mail.fetch(mail_id, '(RFC822)')
        msg = email.message_from_bytes(msg_data[0][1])
        
        for part in msg.walk():
            if part.get_content_disposition() == 'attachment':
                part_filename = part.get_filename()
                if part_filename:
                    normalized_part = re.sub(r'\\s+', ' ', part_filename.strip())
                    normalized_target = re.sub(r'\\s+', ' ', target_filename.strip())
                    
                    if normalized_part == normalized_target or target_filename in part_filename:
                        content = part.get_payload(decode=True)
                        if content:
                            content_base64 = base64.b64encode(content).decode('utf-8')
                            
                            result = {
                                'filename': part_filename,
                                'content_type': part.get_content_type(),
                                'size': len(content),
                                'content': content_base64
                            }
                            
                            print(json.dumps(result))
                            mail.close()
                            mail.logout()
                            sys.exit(0)
    
    print('{"error": "File not found"}')
    mail.close()
    mail.logout()
    
except Exception as e:
    print(f'{{"error": "{str(e)}"}}')
`;

        const escapedScript = pythonScript.replace(/"/g, '\\"').replace(/\$/g, '\\$');
        const result = execSync(`python3 -c "${escapedScript}"`, {
            encoding: 'utf8',
            timeout: 30000
        });

        const fileData = JSON.parse(result.trim());
        
        if (fileData.error) {
            console.error(`❌ Error descargando archivo: ${fileData.error}`);
            return false;
        }

        console.log("✅ Archivo descargado exitosamente:");
        console.log(`   📄 Nombre: ${fileData.filename}`);
        console.log(`   📏 Tamaño: ${(fileData.size / 1024).toFixed(1)} KB`);
        console.log(`   🏷️  Tipo: ${fileData.content_type}`);

        console.log("\n🧪 PASO 3: Enviando correo CON archivo adjunto real");
        
        // Convertir Base64 a Buffer
        const attachmentBuffer = Buffer.from(fileData.content, 'base64');
        
        const emailWithAttachment = {
            from: SENDER_EMAIL,
            to: RECIPIENT,
            subject: "Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA",
            text: "Estimado/a,\n\nAdjunto la factura N° 388 de Real Tissue SPA por $686.470 correspondiente a tasas finales NUVOLA.\n\nSaludos cordiales,\nMarcas Patricia Stocker",
            attachments: [
                {
                    filename: fileData.filename,
                    content: attachmentBuffer,
                    content_type: fileData.content_type
                }
            ]
        };

        console.log("📤 Enviando correo con archivo adjunto...");
        console.log(`   📧 Para: ${emailWithAttachment.to}`);
        console.log(`   📝 Asunto: ${emailWithAttachment.subject}`);
        console.log(`   📎 Archivo: ${fileData.filename} (${(fileData.size / 1024).toFixed(1)} KB)`);

        const attachmentResult = await resend.emails.send(emailWithAttachment);
        
        console.log("\n📊 RESULTADO FINAL:");
        console.log("==================");
        
        if (attachmentResult.error) {
            console.error("❌ ERROR AL ENVIAR CORREO CON ADJUNTO:");
            console.error(JSON.stringify(attachmentResult.error, null, 2));
            return false;
        } else {
            console.log("✅ CORREO CON ADJUNTO ENVIADO EXITOSAMENTE:");
            console.log(`   📧 ID de envío: ${attachmentResult.data?.id}`);
            console.log(`   📊 Data completa: ${JSON.stringify(attachmentResult.data, null, 2)}`);
            return true;
        }

    } catch (error) {
        console.error(`💥 Error en diagnóstico: ${error.message}`);
        console.error(`📋 Stack trace: ${error.stack}`);
        return false;
    }
}

// Ejecutar diagnóstico
testResendAPI().then(success => {
    if (success) {
        console.log("\n🎉 DIAGNÓSTICO COMPLETADO - TODO FUNCIONA CORRECTAMENTE");
        console.log("✅ El correo con archivo adjunto debería haber sido enviado");
        console.log("📧 Revisa la bandeja de <NAME_EMAIL>");
    } else {
        console.log("\n❌ DIAGNÓSTICO COMPLETADO - SE ENCONTRARON PROBLEMAS");
        console.log("🔧 Revisa los errores arriba para identificar el problema");
    }
}).catch(error => {
    console.error(`💥 Error fatal en diagnóstico: ${error.message}`);
});
