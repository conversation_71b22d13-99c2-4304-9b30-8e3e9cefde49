#!/usr/bin/env python3

import imaplib
import email
import base64
import json
import sys

def test_attachment_download():
    """Prueba la descarga de archivos adjuntos desde IMAP"""
    
    # Configuración del servidor IMAP
    config = {
        'host': 'mail.patriciastocker.com',
        'port': 143,
        'username': '<EMAIL>',
        'password': '$Patsto56$'
    }
    
    try:
        print(f"🔗 Conectando a {config['host']}:{config['port']}...")
        
        # Conectar al servidor IMAP
        if config['port'] == 993:
            mail = imaplib.IMAP4_SSL(config['host'], config['port'])
        else:
            mail = imaplib.IMAP4(config['host'], config['port'])
            mail.starttls()
        
        print(f"🔐 Autenticando como {config['username']}...")
        mail.login(config['username'], config['password'])
        
        print("📧 Seleccionando bandeja de entrada...")
        mail.select('inbox')
        
        print("🔍 Buscando correos con archivos adjuntos...")
        status, messages = mail.search(None, 'ALL')
        if status != 'OK':
            print("❌ Error buscando correos")
            return False
            
        # Obtener los últimos 5 correos
        mail_ids = messages[0].split()[-5:]
        print(f"📬 Revisando {len(mail_ids)} correos recientes...")
        
        attachments_found = []
        
        for i, mail_id in enumerate(reversed(mail_ids)):
            print(f"📨 Procesando correo {i+1}/{len(mail_ids)}...")
            
            status, msg_data = mail.fetch(mail_id, '(RFC822)')
            if status != 'OK':
                continue
                
            msg = email.message_from_bytes(msg_data[0][1])
            
            # Información del correo
            subject = msg.get('Subject', 'Sin asunto')
            from_addr = msg.get('From', 'Desconocido')
            print(f"  📄 De: {from_addr}")
            print(f"  📝 Asunto: {subject}")
            
            # Buscar archivos adjuntos
            attachment_count = 0
            for part in msg.walk():
                if part.get_content_disposition() == 'attachment':
                    filename = part.get_filename()
                    if filename:
                        content_type = part.get_content_type()
                        content = part.get_payload(decode=True)
                        size = len(content) if content else 0
                        
                        attachment_info = {
                            'email_subject': subject,
                            'email_from': from_addr,
                            'filename': filename,
                            'content_type': content_type,
                            'size': f"{size} bytes",
                            'size_kb': f"{size/1024:.1f} KB" if size > 0 else "0 KB"
                        }
                        
                        attachments_found.append(attachment_info)
                        attachment_count += 1
                        
                        print(f"    📎 Archivo adjunto: {filename}")
                        print(f"       Tipo: {content_type}")
                        print(f"       Tamaño: {attachment_info['size_kb']}")
            
            if attachment_count == 0:
                print("    ℹ️  Sin archivos adjuntos")
        
        mail.close()
        mail.logout()
        
        print(f"\n✅ Conexión exitosa!")
        print(f"📊 Resumen:")
        print(f"   - Correos revisados: {len(mail_ids)}")
        print(f"   - Archivos adjuntos encontrados: {len(attachments_found)}")
        
        if attachments_found:
            print(f"\n📎 Archivos adjuntos disponibles:")
            for i, att in enumerate(attachments_found, 1):
                print(f"   {i}. {att['filename']} ({att['size_kb']})")
                print(f"      De: {att['email_from']}")
                print(f"      Asunto: {att['email_subject'][:50]}...")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Probando conexión IMAP y descarga de archivos adjuntos...")
    print("=" * 60)
    
    success = test_attachment_download()
    
    print("=" * 60)
    if success:
        print("✅ Prueba completada exitosamente!")
        print("🎉 El sistema puede descargar archivos adjuntos reales.")
    else:
        print("❌ Prueba fallida.")
        print("🔧 Revisa la configuración del servidor IMAP.")
    
    sys.exit(0 if success else 1)
