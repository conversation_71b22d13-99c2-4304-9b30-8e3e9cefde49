#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { spawn } = require('child_process');

class EmailBridgeMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'email-bridge-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_email_attachment',
            description: 'Obtiene un archivo adjunto específico de un correo usando el servidor IMAP',
            inputSchema: {
              type: 'object',
              properties: {
                email_id: {
                  type: 'string',
                  description: 'ID del correo o criterios de búsqueda'
                },
                attachment_name: {
                  type: 'string',
                  description: 'Nombre del archivo adjunto a descargar'
                },
                account_name: {
                  type: 'string',
                  description: 'Nombre de la cuenta de correo',
                  default: 'Marcas Patricia Stocker'
                }
              },
              required: ['email_id', 'attachment_name']
            }
          },
          {
            name: 'search_emails_with_attachments',
            description: 'Busca correos que contengan archivos adjuntos',
            inputSchema: {
              type: 'object',
              properties: {
                search_criteria: {
                  type: 'string',
                  description: 'Criterios de búsqueda para encontrar correos con adjuntos'
                },
                account_name: {
                  type: 'string',
                  description: 'Nombre de la cuenta de correo',
                  default: 'Marcas Patricia Stocker'
                }
              },
              required: ['search_criteria']
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case 'get_email_attachment':
          return await this.handleGetEmailAttachment(request.params.arguments);
        case 'search_emails_with_attachments':
          return await this.handleSearchEmailsWithAttachments(request.params.arguments);
        default:
          throw new Error(`Unknown tool: ${request.params.name}`);
      }
    });
  }

  async handleGetEmailAttachment(args) {
    try {
      console.error(`[Email Bridge] Obteniendo archivo adjunto: ${args.attachment_name} del correo: ${args.email_id}`);
      
      // Llamar al servidor MCP de email para obtener el correo específico
      const emailData = await this.callEmailMCP('get_email_with_attachments', {
        email_id: args.email_id,
        account_name: args.account_name
      });

      if (!emailData || !emailData.attachments) {
        throw new Error('No se encontraron archivos adjuntos en el correo especificado');
      }

      // Buscar el archivo adjunto específico
      const attachment = emailData.attachments.find(att => 
        att.filename === args.attachment_name || 
        att.filename.includes(args.attachment_name)
      );

      if (!attachment) {
        throw new Error(`Archivo adjunto "${args.attachment_name}" no encontrado en el correo`);
      }

      // Descargar el contenido del archivo adjunto
      const attachmentContent = await this.callEmailMCP('download_attachment', {
        email_id: args.email_id,
        attachment_id: attachment.id,
        account_name: args.account_name
      });

      return {
        content: [
          {
            type: 'text',
            text: `✅ **Archivo adjunto obtenido exitosamente**

**Archivo:** ${attachment.filename}
**Tamaño:** ${attachment.size}
**Tipo:** ${attachment.content_type}
**Codificación:** Base64

📎 **Contenido del archivo listo para usar en envío de correos**

El archivo está disponible para ser adjuntado a nuevos correos usando el servidor de envío.`
          }
        ],
        attachment_data: {
          filename: attachment.filename,
          content: attachmentContent.content,
          content_type: attachment.content_type,
          size: attachment.size
        }
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error obteniendo archivo adjunto:** ${error.message}

**Posibles soluciones:**
1. Verificar que el correo existe y tiene archivos adjuntos
2. Confirmar el nombre exacto del archivo adjunto
3. Verificar la conexión con el servidor de correo

**Información proporcionada:**
- Email ID: ${args.email_id}
- Archivo: ${args.attachment_name}
- Cuenta: ${args.account_name}`
          }
        ],
        isError: true
      };
    }
  }

  async handleSearchEmailsWithAttachments(args) {
    try {
      console.error(`[Email Bridge] Buscando correos con adjuntos: ${args.search_criteria}`);
      
      // Llamar al servidor MCP de email para buscar correos
      const searchResults = await this.callEmailMCP('search_emails', {
        query: args.search_criteria,
        has_attachments: true,
        account_name: args.account_name,
        page_size: 10
      });

      if (!searchResults || !searchResults.emails || searchResults.emails.length === 0) {
        return {
          content: [
            {
              type: 'text',
              text: `🔍 **No se encontraron correos con archivos adjuntos**

**Criterios de búsqueda:** ${args.search_criteria}
**Cuenta:** ${args.account_name}

**Sugerencias:**
- Intenta con términos de búsqueda más amplios
- Verifica que los correos tengan archivos adjuntos
- Confirma que la cuenta de correo sea correcta`
            }
          ]
        };
      }

      const emailsList = searchResults.emails.map((email, index) => {
        const attachmentsList = email.attachments ? 
          email.attachments.map(att => `  📎 ${att.filename} (${att.size})`).join('\n') : 
          '  📎 Sin detalles de archivos adjuntos';
        
        return `**${index + 1}. ${email.subject}**
   📧 De: ${email.from}
   📅 Fecha: ${email.date}
   ${attachmentsList}`;
      }).join('\n\n');

      return {
        content: [
          {
            type: 'text',
            text: `📧 **Correos encontrados con archivos adjuntos**

**Criterios de búsqueda:** ${args.search_criteria}
**Resultados:** ${searchResults.emails.length} correo(s)

${emailsList}

💡 **Para descargar un archivo específico, usa:**
\`get_email_attachment\` con el ID del correo y nombre del archivo`
          }
        ],
        search_results: searchResults
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error buscando correos:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async callEmailMCP(action, params) {
    // Simular llamada al servidor MCP de email
    // En implementación real, aquí haríamos la llamada al servidor IMAP
    console.error(`[Email Bridge] Llamando servidor MCP de email: ${action} con parámetros:`, params);
    
    // Por ahora, simular respuestas para testing
    if (action === 'search_emails') {
      return {
        emails: [
          {
            id: 'email_123',
            subject: 'Re: Solicitud Factura - Tasas Finales NUVOLA - 10 UTM',
            from: '<EMAIL>',
            date: '2025-08-06T10:30:00Z',
            attachments: [
              {
                id: 'att_001',
                filename: 'F N 388 REAL TISSUE SPA.pdf',
                size: '245KB',
                content_type: 'application/pdf'
              }
            ]
          }
        ]
      };
    }
    
    if (action === 'get_email_with_attachments') {
      return {
        id: 'email_123',
        subject: 'Re: Solicitud Factura - Tasas Finales NUVOLA - 10 UTM',
        attachments: [
          {
            id: 'att_001',
            filename: 'F N 388 REAL TISSUE SPA.pdf',
            size: '245KB',
            content_type: 'application/pdf'
          }
        ]
      };
    }
    
    if (action === 'download_attachment') {
      return {
        content: 'JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNP...' // Base64 simulado
      };
    }
    
    throw new Error(`Acción no implementada: ${action}`);
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Email Bridge MCP server running on stdio');
  }
}

if (require.main === module) {
  const server = new EmailBridgeMCPServer();
  server.run().catch(console.error);
}

module.exports = EmailBridgeMCPServer;
