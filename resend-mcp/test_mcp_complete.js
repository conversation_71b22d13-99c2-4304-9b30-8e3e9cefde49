#!/usr/bin/env node

// Test completo del servidor MCP con envío real
const { spawn } = require('child_process');
const fs = require('fs');

console.log("🧪 PRUEBA COMPLETA DEL SERVIDOR MCP");
console.log("==================================");

// Configurar variables de entorno
process.env.RESEND_API_KEY = 're_KyieBFKx_6swQaoa3VC1FFAgdYVHghfE9';
process.env.SENDER_EMAIL_ADDRESS = '<EMAIL>';

console.log("✅ Variables de entorno configuradas");

// Simular llamada MCP
const mcpRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/call",
    params: {
        name: "send_email_with_real_attachment",
        arguments: {
            to: "<EMAIL>",
            subject: "Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA",
            body: "Estimado cliente,\n\nAdjunto encontrará la factura solicitada.\n\nSaludos cordiales,\nEquipo Patricia Stocker",
            attachment_filename: "F N 388  REAL TISSUE SPA.pdf"
        }
    }
};

console.log("📧 Preparando envío de correo:");
console.log(`   📨 Para: ${mcpRequest.params.arguments.to}`);
console.log(`   📝 Asunto: ${mcpRequest.params.arguments.subject}`);
console.log(`   📎 Archivo: ${mcpRequest.params.arguments.attachment_filename}`);

// Iniciar servidor MCP
console.log("\n🚀 Iniciando servidor MCP...");

const mcpServer = spawn('node', ['email-attachment-simple.js'], {
    env: process.env,
    stdio: ['pipe', 'pipe', 'pipe']
});

let responseData = '';
let errorData = '';

mcpServer.stdout.on('data', (data) => {
    responseData += data.toString();
});

mcpServer.stderr.on('data', (data) => {
    errorData += data.toString();
    console.log(`📊 Log: ${data.toString().trim()}`);
});

// Enviar request después de un breve delay
setTimeout(() => {
    console.log("\n📤 Enviando request MCP...");
    mcpServer.stdin.write(JSON.stringify(mcpRequest) + '\n');
}, 1000);

// Timeout para la prueba
const testTimeout = setTimeout(() => {
    console.log("\n⏰ Timeout alcanzado - terminando prueba");
    mcpServer.kill();
}, 45000);

mcpServer.on('close', (code) => {
    clearTimeout(testTimeout);
    console.log(`\n🏁 Servidor MCP terminado con código: ${code}`);
    
    if (responseData) {
        console.log("\n📊 RESPUESTA MCP:");
        console.log("================");
        try {
            const response = JSON.parse(responseData);
            console.log(JSON.stringify(response, null, 2));
            
            if (response.result && !response.result.isError) {
                console.log("\n🎉 ¡PRUEBA EXITOSA!");
                console.log("✅ El correo fue enviado correctamente");
                console.log("✅ El archivo adjunto fue descargado y enviado");
            } else {
                console.log("\n❌ PRUEBA FALLIDA");
                console.log("❌ Error en el envío del correo");
            }
        } catch (e) {
            console.log("Raw response:", responseData);
        }
    }
    
    if (errorData) {
        console.log("\n📋 LOGS DEL SERVIDOR:");
        console.log("====================");
        console.log(errorData);
    }
});

mcpServer.on('error', (error) => {
    clearTimeout(testTimeout);
    console.error(`💥 Error del servidor MCP: ${error.message}`);
});

console.log("⏳ Esperando respuesta del servidor MCP...");
