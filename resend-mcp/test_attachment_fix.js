#!/usr/bin/env node

// Test específico para verificar el envío con archivo adjunto
const { spawn } = require('child_process');

console.log("🧪 TEST ESPECÍFICO - ENVÍO CON ARCHIVO ADJUNTO");
console.log("==============================================");

// Configurar variables de entorno
process.env.RESEND_API_KEY = 're_KyieBFKx_6swQaoa3VC1FFAgdYVHghfE9';
process.env.SENDER_EMAIL_ADDRESS = '<EMAIL>';

// Request MCP para envío con archivo adjunto
const mcpRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/call",
    params: {
        name: "send_email_with_real_attachment",
        arguments: {
            to: "<EMAIL>",
            subject: "TEST - Factura N° 388 - Tasas Finales NUVOLA - Real Tissue SPA",
            body: "CORREO DE PRUEBA\n\nEstimado/a,\n\nAdjunto la factura N° 388 de Real Tissue SPA por $686.470 correspondiente a tasas finales NUVOLA.\n\nEste es un correo de prueba para verificar que los archivos adjuntos funcionan correctamente.\n\nSaludos cordiales,\nMarcas Patricia Stocker",
            attachment_filename: "F N 388  REAL TISSUE SPA.pdf"
        }
    }
};

console.log("📧 Configuración del test:");
console.log(`   📨 Para: ${mcpRequest.params.arguments.to}`);
console.log(`   📝 Asunto: ${mcpRequest.params.arguments.subject}`);
console.log(`   📎 Archivo: ${mcpRequest.params.arguments.attachment_filename}`);

// Iniciar servidor MCP
console.log("\n🚀 Iniciando servidor MCP con logging mejorado...");

const mcpServer = spawn('node', ['email-attachment-simple.js'], {
    env: process.env,
    stdio: ['pipe', 'pipe', 'pipe']
});

let responseData = '';
let errorData = '';
let responseReceived = false;

mcpServer.stdout.on('data', (data) => {
    const chunk = data.toString();
    responseData += chunk;
    
    // Verificar si recibimos una respuesta JSON completa
    try {
        JSON.parse(responseData);
        responseReceived = true;
        console.log("\n📨 Respuesta MCP recibida!");
    } catch (e) {
        // Aún no es JSON completo
    }
});

mcpServer.stderr.on('data', (data) => {
    const logLine = data.toString().trim();
    errorData += logLine + '\n';
    console.log(`📊 ${logLine}`);
});

// Enviar request después de un breve delay
setTimeout(() => {
    console.log("\n📤 Enviando request MCP...");
    mcpServer.stdin.write(JSON.stringify(mcpRequest) + '\n');
}, 1000);

// Timeout para la prueba
const testTimeout = setTimeout(() => {
    if (!responseReceived) {
        console.log("\n⏰ Timeout - terminando prueba");
        mcpServer.kill();
    }
}, 60000);

mcpServer.on('close', (code) => {
    clearTimeout(testTimeout);
    console.log(`\n🏁 Servidor MCP terminado con código: ${code}`);
    
    if (responseData) {
        console.log("\n📊 RESPUESTA MCP:");
        console.log("================");
        try {
            const response = JSON.parse(responseData);
            console.log(JSON.stringify(response, null, 2));
            
            if (response.result && !response.result.isError) {
                console.log("\n🎉 ¡PRUEBA EXITOSA!");
                console.log("✅ El correo fue enviado correctamente");
                console.log("✅ Revisa tu bandeja de <NAME_EMAIL>");
                console.log("✅ También revisa el dashboard de Resend para confirmar el envío");
            } else if (response.error) {
                console.log("\n❌ ERROR EN MCP:");
                console.log(`❌ ${response.error.message}`);
            } else {
                console.log("\n❌ RESPUESTA INESPERADA");
            }
        } catch (e) {
            console.log("❌ Error parseando respuesta JSON:");
            console.log(responseData);
        }
    } else {
        console.log("\n❌ No se recibió respuesta del servidor MCP");
    }
    
    console.log("\n📋 LOGS COMPLETOS:");
    console.log("==================");
    console.log(errorData);
});

mcpServer.on('error', (error) => {
    clearTimeout(testTimeout);
    console.error(`💥 Error del servidor MCP: ${error.message}`);
});

console.log("⏳ Esperando respuesta del servidor MCP (máximo 60 segundos)...");
