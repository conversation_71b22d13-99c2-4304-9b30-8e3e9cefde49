#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { execSync } = require('child_process');

class EmailSearchMCP {
  constructor() {
    this.server = new Server(
      {
        name: 'email-search-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'search_emails_from_sender',
            description: 'Busca correos de un remitente específico y lista archivos adjuntos',
            inputSchema: {
              type: 'object',
              properties: {
                sender_email: {
                  type: 'string',
                  description: 'Email del remitente a buscar (ej: <EMAIL>)'
                },
                search_term: {
                  type: 'string',
                  description: 'Término de búsqueda opcional (ej: factura)',
                  default: ''
                },
                max_results: {
                  type: 'number',
                  description: 'Número máximo de correos a revisar',
                  default: 10
                }
              },
              required: ['sender_email']
            }
          },
          {
            name: 'get_email_attachments',
            description: 'Lista todos los archivos adjuntos disponibles en correos recientes',
            inputSchema: {
              type: 'object',
              properties: {
                max_emails: {
                  type: 'number',
                  description: 'Número máximo de correos a revisar',
                  default: 20
                }
              }
            }
          }
        ]
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case 'search_emails_from_sender':
          return await this.handleSearchEmailsFromSender(request.params.arguments);
        case 'get_email_attachments':
          return await this.handleGetEmailAttachments(request.params.arguments);
        default:
          throw new Error(`Unknown tool: ${request.params.name}`);
      }
    });
  }

  async handleSearchEmailsFromSender(args) {
    try {
      console.error(`[Email Search MCP] Buscando correos de: ${args.sender_email}`);
      
      const pythonScript = `
import imaplib
import email
import json
import sys
from email.header import decode_header

def decode_mime_words(s):
    if s is None:
        return ""
    decoded_parts = decode_header(s)
    decoded_string = ""
    for part, encoding in decoded_parts:
        if isinstance(part, bytes):
            if encoding:
                decoded_string += part.decode(encoding)
            else:
                decoded_string += part.decode('utf-8', errors='ignore')
        else:
            decoded_string += part
    return decoded_string

try:
    # Conectar al servidor IMAP
    print("🔗 Conectando a servidor IMAP...", file=sys.stderr)
    mail = imaplib.IMAP4('mail.patriciastocker.com', 143)
    mail.starttls()
    mail.login('<EMAIL>', '$Patsto56$')
    mail.select('inbox')
    print("✅ Conexión IMAP exitosa", file=sys.stderr)
    
    # Buscar correos del remitente específico
    sender_email = "${args.sender_email}"
    search_term = "${args.search_term || ''}"
    max_results = ${args.max_results || 10}
    
    print(f"🔍 Buscando correos de: {sender_email}", file=sys.stderr)
    
    # Buscar todos los correos
    status, messages = mail.search(None, 'ALL')
    mail_ids = messages[0].split()[-max_results:]
    
    found_emails = []
    
    for mail_id in reversed(mail_ids):
        status, msg_data = mail.fetch(mail_id, '(RFC822)')
        msg = email.message_from_bytes(msg_data[0][1])
        
        from_addr = decode_mime_words(msg.get("From", ""))
        subject = decode_mime_words(msg.get("Subject", ""))
        date = msg.get("Date", "")
        
        # Verificar si es del remitente buscado
        if sender_email.lower() in from_addr.lower():
            # Si hay término de búsqueda, verificar que esté en el asunto
            if not search_term or search_term.lower() in subject.lower():
                print(f"📧 Correo encontrado: {subject}", file=sys.stderr)
                
                # Buscar archivos adjuntos
                attachments = []
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            filename = decode_mime_words(filename)
                            content_type = part.get_content_type()
                            content = part.get_payload(decode=True)
                            size = len(content) if content else 0
                            
                            attachments.append({
                                'filename': filename,
                                'content_type': content_type,
                                'size': size,
                                'size_kb': f"{size/1024:.1f} KB" if size > 0 else "0 KB"
                            })
                            print(f"    📎 Archivo: {filename} ({size/1024:.1f} KB)", file=sys.stderr)
                
                found_emails.append({
                    'from': from_addr,
                    'subject': subject,
                    'date': date,
                    'attachments': attachments,
                    'attachment_count': len(attachments)
                })
    
    result = {
        'sender': sender_email,
        'search_term': search_term,
        'emails_found': len(found_emails),
        'emails': found_emails
    }
    
    print(json.dumps(result))
    mail.close()
    mail.logout()
    
except Exception as e:
    print(f"💥 Error: {str(e)}", file=sys.stderr)
    print(f'{{"error": "{str(e)}"}}')
`;

      const escapedScript = pythonScript.replace(/"/g, '\\"').replace(/\$/g, '\\$');
      const result = execSync(`python3 -c "${escapedScript}"`, {
        encoding: 'utf8',
        timeout: 30000
      });

      const data = JSON.parse(result.trim());
      
      if (data.error) {
        throw new Error(data.error);
      }

      return {
        content: [
          {
            type: 'text',
            text: `📧 **Búsqueda de correos de ${data.sender}**

**Resultados encontrados:** ${data.emails_found} correos

${data.emails.map((email, index) => `
**Correo ${index + 1}:**
- **De:** ${email.from}
- **Asunto:** ${email.subject}
- **Fecha:** ${email.date}
- **Archivos adjuntos:** ${email.attachment_count}

${email.attachments.map(att => `  📎 **${att.filename}** (${att.size_kb}) - ${att.content_type}`).join('\n')}
`).join('\n---\n')}

${data.emails_found === 0 ? '❌ No se encontraron correos del remitente especificado.' : '✅ Búsqueda completada exitosamente.'}`
          }
        ]
      };

    } catch (error) {
      console.error(`[Email Search MCP] Error: ${error.message}`);
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error buscando correos:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async handleGetEmailAttachments(args) {
    try {
      console.error(`[Email Search MCP] Listando archivos adjuntos...`);
      
      const pythonScript = `
import imaplib
import email
import json
import sys
from email.header import decode_header

def decode_mime_words(s):
    if s is None:
        return ""
    decoded_parts = decode_header(s)
    decoded_string = ""
    for part, encoding in decoded_parts:
        if isinstance(part, bytes):
            if encoding:
                decoded_string += part.decode(encoding)
            else:
                decoded_string += part.decode('utf-8', errors='ignore')
        else:
            decoded_string += part
    return decoded_string

try:
    # Conectar al servidor IMAP
    mail = imaplib.IMAP4('mail.patriciastocker.com', 143)
    mail.starttls()
    mail.login('<EMAIL>', '$Patsto56$')
    mail.select('inbox')
    
    # Buscar correos recientes
    status, messages = mail.search(None, 'ALL')
    mail_ids = messages[0].split()[-${args.max_emails || 20}:]
    
    all_attachments = []
    
    for mail_id in reversed(mail_ids):
        status, msg_data = mail.fetch(mail_id, '(RFC822)')
        msg = email.message_from_bytes(msg_data[0][1])
        
        from_addr = decode_mime_words(msg.get("From", ""))
        subject = decode_mime_words(msg.get("Subject", ""))
        
        # Buscar archivos adjuntos
        for part in msg.walk():
            if part.get_content_disposition() == 'attachment':
                filename = part.get_filename()
                if filename:
                    filename = decode_mime_words(filename)
                    content_type = part.get_content_type()
                    content = part.get_payload(decode=True)
                    size = len(content) if content else 0
                    
                    all_attachments.append({
                        'filename': filename,
                        'content_type': content_type,
                        'size': size,
                        'size_kb': f"{size/1024:.1f} KB" if size > 0 else "0 KB",
                        'email_from': from_addr,
                        'email_subject': subject
                    })
    
    result = {
        'total_attachments': len(all_attachments),
        'attachments': all_attachments
    }
    
    print(json.dumps(result))
    mail.close()
    mail.logout()
    
except Exception as e:
    print(f'{{"error": "{str(e)}"}}')
`;

      const escapedScript = pythonScript.replace(/"/g, '\\"').replace(/\$/g, '\\$');
      const result = execSync(`python3 -c "${escapedScript}"`, {
        encoding: 'utf8',
        timeout: 30000
      });

      const data = JSON.parse(result.trim());
      
      if (data.error) {
        throw new Error(data.error);
      }

      return {
        content: [
          {
            type: 'text',
            text: `📎 **Lista de archivos adjuntos disponibles**

**Total de archivos encontrados:** ${data.total_attachments}

${data.attachments.map((att, index) => `
**${index + 1}. ${att.filename}**
- **Tamaño:** ${att.size_kb}
- **Tipo:** ${att.content_type}
- **De:** ${att.email_from}
- **Asunto:** ${att.email_subject.substring(0, 50)}${att.email_subject.length > 50 ? '...' : ''}
`).join('\n')}

✅ Lista completada exitosamente.`
          }
        ]
      };

    } catch (error) {
      console.error(`[Email Search MCP] Error: ${error.message}`);
      return {
        content: [
          {
            type: 'text',
            text: `❌ **Error listando archivos adjuntos:** ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Email Search MCP server running on stdio');
  }
}

if (require.main === module) {
  const server = new EmailSearchMCP();
  server.run().catch(console.error);
}
